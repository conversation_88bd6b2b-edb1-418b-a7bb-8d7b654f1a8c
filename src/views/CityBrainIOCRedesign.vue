<template>
  <div class="bg-gradient-to-b from-dark to-dark-light text-gray-100 min-h-screen font-sans overflow-x-hidden">
    <!-- 背景装饰 -->
    <div class="fixed inset-0 z-0 opacity-10">
      <div class="absolute top-0 left-0 w-full h-full bg-[url('https://picsum.photos/id/1/1920/1080')] bg-cover bg-center"></div>
      <div class="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_center,rgba(14,165,233,0.2),transparent_70%)]"></div>
    </div>

    <!-- 顶部状态栏 -->
    <header class="relative z-10 glass border-b border-gray-700/50">
      <div class="container mx-auto px-4 py-3 flex justify-between items-center">
        <div class="flex items-center space-x-2">
          <i class="fa fa-brain text-primary text-xl glow"></i>
          <span class="font-semibold">城市大脑IOC系统</span>
        </div>
        
        <div class="hidden md:flex items-center space-x-6">
          <div class="flex items-center space-x-2">
            <span class="text-xs text-gray-400">系统状态</span>
            <span class="w-2 h-2 rounded-full bg-green-500 animate-pulse"></span>
          </div>
          <div class="flex items-center space-x-2">
            <span class="text-xs text-gray-400">在线设备</span>
            <span class="text-xs">{{ systemStats.onlineDevices }}</span>
          </div>
          <div class="flex items-center space-x-2">
            <span class="text-xs text-gray-400">数据传输</span>
            <span class="text-xs">{{ systemStats.dataTransfer }}</span>
          </div>
        </div>
        
        <div class="flex items-center space-x-4">
          <div class="text-xs bg-dark-light px-3 py-1 rounded-full">
            <span class="text-primary">实时监控中</span>
          </div>
          <div class="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center">
            <i class="fa fa-user text-primary"></i>
          </div>
        </div>
      </div>
    </header>

    <!-- 主标题 -->
    <div class="relative z-10 container mx-auto px-4 py-8 text-center">
      <h1 class="text-[clamp(2rem,6vw,3.5rem)] font-bold text-transparent bg-clip-text bg-gradient-to-r from-primary via-secondary to-tertiary text-shadow">
        城市大脑智能运行中心
      </h1>
      <p class="mt-4 text-gray-300 max-w-2xl mx-auto text-[clamp(1rem,2vw,1.2rem)]">
        基于人体神经网络架构的城市智能管理系统 — 感知、分析、决策、执行
      </p>
    </div>

    <!-- 主要内容区 -->
    <main class="relative z-10 container mx-auto px-4 pb-16">
      <!-- 大脑核心区域 -->
      <section class="relative mb-20 flex justify-center">
        <div class="relative w-full max-w-2xl aspect-square max-h-[60vh]">
          <!-- 大脑容器 -->
          <div class="absolute inset-0 flex items-center justify-center">
            <div class="relative w-[90%] h-[90%] rounded-full bg-gradient-to-br from-primary/20 to-secondary/20 animate-float overflow-hidden border border-primary/30">
              <!-- 大脑纹理 -->
              <div class="absolute inset-0 bg-[url('https://picsum.photos/id/100/600/600')] bg-cover bg-center opacity-10 mix-blend-overlay"></div>
              
              <!-- 核心标识 -->
              <div class="absolute inset-0 flex flex-col items-center justify-center">
                <i class="fa fa-brain text-[clamp(6rem,20vw,10rem)] text-primary/60 animate-pulse-slow glow"></i>
                <div class="mt-4 text-center">
                  <h2 class="text-[clamp(1.5rem,4vw,2.5rem)] font-bold text-white text-shadow">城市大脑</h2>
                  <p class="text-primary/80 text-sm mt-1">神经中枢系统</p>
                </div>
              </div>
              
              <!-- 活动节点 -->
              <div 
                v-for="(node, index) in brainNodes" 
                :key="index"
                class="absolute w-3 h-3 rounded-full animate-pulse"
                :class="node.color"
                :style="{ top: node.top, left: node.left, animationDelay: node.delay }"
              ></div>
            </div>
          </div>
          
          <!-- 大脑性能指标 -->
          <div class="absolute -bottom-6 left-1/2 transform -translate-x-1/2 glass rounded-full px-6 py-2 border border-gray-700/50 flex items-center space-x-6">
            <div v-for="metric in brainMetrics" :key="metric.label" class="flex flex-col items-center">
              <span class="text-xs text-gray-400">{{ metric.label }}</span>
              <span :class="`text-${metric.color} font-semibold`">{{ metric.value }}</span>
            </div>
          </div>
        </div>
      </section>

      <!-- 神经元卡片区域 -->
      <div class="relative grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8 mt-6">
        <div 
          v-for="card in neuronCards" 
          :key="card.id"
          class="neuron-card glass rounded-xl p-6 border transition-all duration-300 transform hover:-translate-y-2 group relative overflow-hidden"
          :class="`border-${card.color}/30 hover:border-${card.color}/60`"
        >
          <!-- 背景装饰 -->
          <div :class="`absolute top-0 right-0 w-32 h-32 bg-${card.color}/10 rounded-full -mr-16 -mt-16 blur-2xl`"></div>
          
          <div class="relative z-10">
            <div :class="`w-16 h-16 rounded-full bg-${card.color}/20 flex items-center justify-center mb-5 group-hover:bg-${card.color}/30 transition-colors`">
              <i :class="`fa ${card.icon} text-2xl text-${card.color} glow`"></i>
            </div>
            <h3 class="text-xl font-semibold mb-3 text-white flex items-center">
              {{ card.title }}
              <span :class="`ml-2 text-xs bg-${card.color}/20 text-${card.color} px-2 py-0.5 rounded-full`">{{ card.badge }}</span>
            </h3>
            <p class="text-gray-300 text-sm mb-6">{{ card.description }}</p>
            
            <!-- 数据指标 -->
            <div class="space-y-4">
              <div v-for="metric in card.metrics" :key="metric.label">
                <div class="flex justify-between text-xs mb-1">
                  <span class="text-gray-400">{{ metric.label }}</span>
                  <span :class="`text-${card.color}`">{{ metric.value }}</span>
                </div>
                <div class="w-full h-1.5 bg-gray-700/50 rounded-full overflow-hidden">
                  <div :class="`h-full bg-${card.color} rounded-full`" :style="{ width: metric.percentage }"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- 页脚 -->
    <footer class="relative z-10 mt-20 py-6 px-4 border-t border-gray-800/50">
      <div class="container mx-auto">
        <div class="flex flex-col md:flex-row justify-between items-center">
          <div class="flex items-center space-x-2 mb-4 md:mb-0">
            <i class="fa fa-brain text-primary text-lg glow"></i>
            <span class="font-semibold">城市大脑IOC智能运行中心</span>
          </div>
          
          <div class="text-sm text-gray-400">
            &copy; 2023 城市智能管理系统 版权所有 | 版本 v2.4.8
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 系统统计数据
const systemStats = ref({
  onlineDevices: '24,587',
  dataTransfer: '12.8 GB/s'
})

// 大脑活动节点
const brainNodes = ref([
  { top: '30%', left: '25%', color: 'bg-primary', delay: '0s' },
  { top: '45%', left: '70%', color: 'bg-secondary', delay: '0.5s' },
  { top: '65%', left: '35%', color: 'bg-tertiary', delay: '1s' },
  { top: '20%', left: '60%', color: 'bg-quaternary', delay: '1.5s' }
])

// 大脑性能指标
const brainMetrics = ref([
  { label: 'CPU使用率', value: '37%', color: 'primary' },
  { label: '内存占用', value: '52%', color: 'secondary' },
  { label: '响应时间', value: '12ms', color: 'tertiary' },
  { label: '系统负载', value: '中等', color: 'quaternary' }
])

// 神经元卡片数据
const neuronCards = ref([
  {
    id: 1,
    title: 'AI中枢',
    badge: '核心',
    icon: 'fa-microchip',
    color: 'primary',
    description: '城市智能决策核心，通过深度学习算法分析城市运行数据，提供预测性分析和智能决策支持。',
    metrics: [
      { label: '模型准确率', value: '97.2%', percentage: '97.2%' },
      { label: '实时分析速度', value: '1,245 次/秒', percentage: '85%' }
    ]
  },
  {
    id: 2,
    title: '数据中枢',
    badge: '存储',
    icon: 'fa-database',
    color: 'tertiary',
    description: '城市数据的统一存储和管理中心，整合多源异构数据，提供高效的数据查询和分析服务。',
    metrics: [
      { label: '数据总量', value: '145.8 PB', percentage: '68%' },
      { label: '数据完整性', value: '99.9%', percentage: '99.9%' }
    ]
  },
  {
    id: 3,
    title: '感知网络',
    badge: '采集',
    icon: 'fa-wifi',
    color: 'quaternary',
    description: '城市感知设备网络，实时采集城市各类数据，包括交通、环境、安全等多维度信息。',
    metrics: [
      { label: '设备在线率', value: '98.4%', percentage: '98.4%' },
      { label: '数据采集频率', value: '256 Hz', percentage: '92%' }
    ]
  },
  {
    id: 4,
    title: '执行网络',
    badge: '控制',
    icon: 'fa-cogs',
    color: 'secondary',
    description: '城市设备控制网络，根据AI决策结果执行相应的控制指令，实现城市系统的智能化管理。',
    metrics: [
      { label: '执行成功率', value: '99.1%', percentage: '99.1%' },
      { label: '响应延迟', value: '8ms', percentage: '88%' }
    ]
  }
])

onMounted(() => {
  // 为中枢卡片添加滚动动画
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('opacity-100', 'translate-y-0')
        entry.target.classList.remove('opacity-0', 'translate-y-10')
      }
    })
  }, { threshold: 0.1 })
  
  // 为所有神经元卡片添加初始样式和观察器
  document.querySelectorAll('.neuron-card').forEach(card => {
    card.classList.add('transition-all', 'duration-1000', 'opacity-0', 'translate-y-10')
    observer.observe(card)
  })
})
</script>

<style scoped>
@import url('https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap');

.content-auto {
  content-visibility: auto;
}

.brain-connection {
  stroke-dasharray: 15;
  animation: dash 8s linear infinite;
}

.nerve-pulse {
  stroke-dasharray: 10;
  animation: dash 2s linear infinite;
}

.glow {
  filter: drop-shadow(0 0 8px currentColor);
}

.text-shadow {
  text-shadow: 0 0 8px currentColor;
}

.glass {
  background: rgba(30, 41, 59, 0.6);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

@keyframes dash {
  to {
    stroke-dashoffset: 100;
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-pulse-soft {
  animation: pulse-soft 2s infinite;
}

@keyframes pulse-soft {
  0%, 100% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.05); opacity: 1; }
}
</style>